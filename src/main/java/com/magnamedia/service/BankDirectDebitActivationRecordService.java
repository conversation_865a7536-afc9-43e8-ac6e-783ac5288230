package com.magnamedia.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.BankDirectDebitActivationFileController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.BankDirectDebitActivationFile;
import com.magnamedia.entity.BankDirectDebitActivationRecord;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDActivationFileStatus;
import com.magnamedia.module.type.DirectDebitBankRejectionReason;
import com.magnamedia.module.type.DirectDebitFileStatus;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.report.NewDDRejectionReasonsReport;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.BankDirectDebitActivationFileRepository;
import com.magnamedia.repository.BankDirectDebitActivationRecordRepository;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.controller.BankDirectDebitActivationFileController.BANK_RESPONSE_ACCEPTED_STATUS;
import static com.magnamedia.controller.BankDirectDebitActivationFileController.BANK_RESPONSE_REJECTED_STATUS;

@Service
public class BankDirectDebitActivationRecordService {
    private static final Logger logger = Logger.getLogger(BankDirectDebitActivationRecordService.class.getName());

    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private BankDirectDebitActivationRecordRepository bankDirectDebitActivationRecordRepository;
    @Autowired
    private BankDirectDebitActivationFileRepository bankDirectDebitActivationFileRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;

    @Transactional
    public String confirmDDsFromActivationFile(List<BankDirectDebitActivationRecord> records, boolean fromRPA) {
        if (records == null || records.isEmpty()) {
            logger.info("No activation records found");
            return "records is being processed. We will send you an email once done.!";
        };

        BankDirectDebitActivationFile file = records.get(0).getBankDirectDebitActivationFile();
        if (QueryService.existsEntity(BackgroundTask.class, "e.relatedEntityType = :p0 and e.relatedEntityId <> :p1 and e.status not in :p2",
                new Object[] {"BankDirectDebitActivationFile", file.getId(), Arrays.asList(
                        BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)})) {
            try {
                // Create property to be handled by AccountingModuleMainJob
                AccountingEntityProperty a = new AccountingEntityProperty();
                a.setKey(AccountingModule.RUN_BACKGROUND_TASK_AFTER_CHECK_RELATED_ENTITY_TYPE);
                // Related Entity Type
                a.setPurpose("BankDirectDebitActivationFile");
                a.setValue(objectMapper.writeValueAsString(new HashMap<String, String>() {{
                    put("fileId", String.valueOf(file.getId()));
                }}));
                a.setOrigin(file);
                accountingEntityPropertyRepository.save(a);
            } catch (Exception e) {
                logger.info("Error in create property for confirm file id : " + file.getId());
                e.printStackTrace();
            }
        } else {
            confirmMatchedRecords(records, fromRPA);
        }

        return "records is being processed. We will send you an email once done.!";
    }

    public void createBGTForConfirmActivationRecords(String body) {
        try {
            Map<String, String> payload = objectMapper.readValue(body, new TypeReference<Map<String, String>>(){});

            Long fileId = Long.parseLong(payload.get("fileId"));
            backgroundTaskService.create(new BackgroundTask.builder(
                    "Pre_Confirm_dds_Activation_File_" + fileId,
                    "accounting",
                    "bankDirectDebitActivationRecordService",
                    "confirmMatchedRecordsWithIds")
                    .withRelatedEntity("BankDirectDebitActivationFile", fileId)
                    .withParameters(
                        new Class[] { Long.class },
                        new Object[] { fileId })
                    .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                    .build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Transactional
    public void confirmMatchedRecordsWithIds(Long fileId) {
        confirmMatchedRecords(bankDirectDebitActivationRecordRepository
                .findMatchedRecordsIdsByFileId(fileId), true);
    }

    @Transactional
    public void confirmMatchedRecords(List<BankDirectDebitActivationRecord> records, Boolean fromRPA) {
        long time = new Date().getTime();
        logger.info("records size :" + records.size() + " fromRPA : " + fromRPA);

        records.removeIf(r ->
                r.getDirectDebitFile() == null ||
                r.getDirectDebitFile().getDirectDebit() == null ||
                r.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() == null ||
                r.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() == null);
        if (records.isEmpty()) return;

        for (BankDirectDebitActivationRecord r : records) {
            r.setProcessing(true);
            r.setProcessingTimeStamp(time);
        }
        bankDirectDebitActivationRecordRepository.saveAllAndFlush(records);
        logger.info(" records ids : " + records.stream().map(BaseEntity::getId).collect(Collectors.toList()));
        //CALL ASYNC FUNCTION
        preConfirmDDsApiAsync(records, fromRPA,
                CurrentRequest.getUser() != null ? CurrentRequest.getUser().getEmail() : null);
    }

    // ACC-9004
    public void sendReportInEmail(Long fileId, Long matchedAndAcceptedCount, Long matchedAndRejectedCount, Long unMatchedCount) {
        BankDirectDebitActivationFile file = bankDirectDebitActivationFileRepository.findOne(fileId);

        Map<String, String> parameters = new HashMap<>();

        parameters.put("report_name", file.getAttachments().get(0).getName());
        parameters.put("upload_date", new LocalDate(file.getDate()).toString("yyyy-MM-dd"));
        // ACC-9366
        parameters.put("matched_accepted_count", Long.toString(matchedAndAcceptedCount));
        parameters.put("matched_rejected_count", Long.toString(matchedAndRejectedCount));
        parameters.put("unmatched_count", Long.toString(unMatchedCount));
        parameters.put("details_link", Setup.getApplicationContext()
                .getBean(Utils.class)
                .shorteningUrl(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                        "#!/accounting/payments-automation/importing-file/ddFile/" + file.getId()));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_300_activation_report", parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DDS_ACTIVATION_REPORT_RPA_MAIL),
                        "DD 300 Activation Report");
    }

    public Page<BankDirectDebitActivationRecord> getRecords(
            BankDirectDebitActivationFile file, BankDirectDebitActivationFileController.recordMatched matched,
            boolean rejected, Pageable pageable) {
        SelectQuery<BankDirectDebitActivationRecord> query =
                new SelectQuery<>(BankDirectDebitActivationRecord.class);
        query.filterBy(CurrentRequest.getSearchFilter());
        query.filterBy("bankDirectDebitActivationFile", "=", file);
        if (matched == null)
            matched = BankDirectDebitActivationFileController.recordMatched.MATCHED;
        switch (matched) {
            case MATCHED:
                query.filterBy("status", "LIKE", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                query.filterBy("directDebitFileId", "IS NOT NULL", null);
                query.filterBy("ddStatus", "=", DirectDebitStatus.PENDING);
                if (pageable == null || pageable.getSort().isUnsorted()) {
                    query.sortBy("ddMethod", true, true);
                    query.sortBy("id", true, true);
                }
                break;
            case NOT_MATCHED:
                query.filterBy("status", "LIKE", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                query.filterBy("directDebitFileId", "IS NULL", null);

                break;
            case PREV_MATCHED:
                query.filterBy("directDebitFileId", "IS NOT NULL", null);
                query.filterBy("ddStatus", "!=", DirectDebitStatus.PENDING);
                if (pageable == null || pageable.getSort().isUnsorted()) {
                    query.sortBy("ddMethod", true, true);
                    query.sortBy("id", true, true);
                }
                break;
        }

        if (pageable == null) {
            return new PageImpl(query.execute());
        } else {
            return query.execute(pageable);
        }
    }

    public boolean existsRecords(BankDirectDebitActivationFile file, BankDirectDebitActivationFileController.recordMatched matched, boolean rejected) {
        StringBuilder conditions = new StringBuilder();
        Map<String, Object> parameters = new HashMap<>();

        parameters.put("file", file);
        conditions.append("e.bankDirectDebitActivationFile = :file");

        if (matched == null)
            matched = BankDirectDebitActivationFileController.recordMatched.MATCHED;

        switch (matched) {
            case MATCHED:
                conditions.append(" and e.status like :status");
                conditions.append(" and e.directDebitFileId is not null");
                conditions.append(" and e.ddStatus = :ddStatus");
                parameters.put("status", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                parameters.put("ddStatus", DirectDebitStatus.PENDING);
                break;
            case NOT_MATCHED:
                conditions.append(" and e.status like :status");
                conditions.append(" and e.directDebitFileId is null");
                parameters.put("status", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                break;
            case PREV_MATCHED:
                conditions.append(" and e.directDebitFileId is not null");
                conditions.append(" and e.ddStatus != :ddStatus");
                parameters.put("ddStatus", DirectDebitStatus.PENDING);
                break;
        }

        String query = "select count(e.id) > 0 from BankDirectDebitActivationRecord e where " + conditions.toString();

        return new SelectQuery<>(query, "", Boolean.class, parameters)
                .execute().get(0);
    }

    @Async
    @Transactional
    public void preConfirmDDsApiAsync(List<BankDirectDebitActivationRecord> records, Boolean fromRPA, String email) {
        preConfirmDDsAPI(records, fromRPA, email);
    }

    @Transactional
    public void preConfirmDDsAPI(List<BankDirectDebitActivationRecord> records, Boolean fromRPA, String email) {
        logger.info("ids size :  " + (records != null ? records.size() : null));
        if (records == null || records.isEmpty()) return;

        Map<Long, List<BankDirectDebitActivationRecord>> recordsGroupingByContract = records.stream()
                .filter(r -> r.getDirectDebitFile() != null &&
                        !Arrays.asList(DirectDebitFileStatus.APPROVED, DirectDebitFileStatus.REJECTED)
                                .contains(r.getDirectDebitFile().getStatus()))
                .collect(Collectors.groupingBy(r ->
                        r.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId()));

        String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_ACTIVATION_RECORD);

        recordsGroupingByContract.forEach((contractId, recordsOfContract) -> {
            if (!recordsOfContract.isEmpty()) {
                backgroundTaskService.create(new BackgroundTask.builder(
                        "BankDirectDebitActivationFile_Confirm_DD_" + recordsOfContract.get(0).getBankDirectDebitActivationFile().getId(),
                        "accounting",
                        "bankDirectDebitActivationFileController",
                        "confirmDDs")
                        .withRelatedEntity("BankDirectDebitActivationFile", recordsOfContract.get(0).getBankDirectDebitActivationFile().getId())
                        .withParameters(
                                new Class[] { List.class, Boolean.class, String.class },
                                new Object[] { recordsOfContract.stream()
                                        .map(x -> x.getId().toString())
                                        .collect(Collectors.toList()),
                                        fromRPA, email })
                        .withQueue(BackgroundTaskQueues.valueOf(queue))
                        .withDelay(recordsOfContract.stream().anyMatch(b -> "REJECTED".equals(b.getStatus())) ? 3 * 60 * 1000L : 0L)
                        .build());
            }
        });
    }

    // ACC-330
    public void parseRecords(BankDirectDebitActivationFile bankDirectDebitActivationFile) throws IOException {
        List<String> newRejectionReasons = new ArrayList();

        // Prepare rejection reasons data once before processing
        List<String> knownDDRejectionPickListItems = Setup.getRepository(PicklistItemRepository.class)
                .findByListOrderByNameAsc(
                        Setup.getRepository(PicklistRepository.class)
                                .findByCode(AccountingModule.PICKLIST_KNOWN_DD_REJECTION_REASONS))
                .stream()
                .map(PicklistItem::getCode)
                .collect(Collectors.toList());

        List<BankDirectDebitActivationRecord> records = parseRecordsInMemory(bankDirectDebitActivationFile);

        records.forEach(r -> {
                    // ACC-2777
                    if (r.getRejectionReason() != null && !r.getRejectionReason().isEmpty() &&
                            !knownDDRejectionPickListItems.contains(r.getRejectionReason()
                                    .toLowerCase().replaceAll(" ", "_")) &&
                            !newRejectionReasons.contains(r.getRejectionReason())) {
                        newRejectionReasons.add(r.getRejectionReason());
                    }
                });

        records = bankDirectDebitActivationRecordRepository.save(records);

        int unProcessRecordsSize = records.size();
        Integer pageNb = 0;
        BankDirectDebitActivationRecordService bankDirectDebitActivationRecordService =
                Setup.getApplicationContext().getBean(BankDirectDebitActivationRecordService.class);
        while (unProcessRecordsSize > 0) {
            bankDirectDebitActivationRecordService.validateRecordsAfterInsert(pageNb++, 200, bankDirectDebitActivationFile.getId());
            unProcessRecordsSize -= 200;
        }

        notifyAccountantAboutNewRejectionReasons(records, newRejectionReasons);
    }

    /**
     * ACC-9606 Update the Method:
     * Only parse the records without any perform any save operation.
     */
    public List<BankDirectDebitActivationRecord> parseRecordsInMemory(BankDirectDebitActivationFile bankDirectDebitActivationFile) throws IOException {

        logger.info("bankDirectDebitActivationFile: " +  new ObjectMapper().writeValueAsString(bankDirectDebitActivationFile));
        if (bankDirectDebitActivationFile.getAttachments() == null || bankDirectDebitActivationFile.getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");

        Attachment att = bankDirectDebitActivationFile.getAttachments().get(0);
        List<BankDirectDebitActivationRecord> records = new ArrayList<>();

        BufferedReader br = new BufferedReader(new InputStreamReader(Storage.getStream(att)));
        // Configure CSVReader with optimal settings for performance
        CSVReader csvReader = new CSVReaderBuilder(br)
                .withCSVParser(new CSVParserBuilder().withSeparator(',').build()) // Use comma as separator
                .withSkipLines(5) // Skip first 5 lines manually before creating the CSV reader
                .withKeepCarriageReturn(false) // Improve performance by not keeping carriage returns
                .withVerifyReader(false) // Disable reader verification for better performance
                .build();

        String[] cellsValues;
        // Read and process records in batches
        while ((cellsValues = csvReader.readNext()) != null) {
            // Skip empty rows
            if (cellsValues.length == 0) continue;
            BankDirectDebitActivationRecord activationRecord = new BankDirectDebitActivationRecord();
            try {
                // Parse the CSV record into an activation record
                parseRecordFields(cellsValues, activationRecord);

                // Process rejection reason if present
                if (activationRecord.getRejectionReason() != null) {
                    processRejectionReason(activationRecord);
                }
            } catch (Exception ex) {
                handleRecordException(ex, activationRecord);
            } finally {
                logger.info("activationRecord: " +  activationRecord.getRowIndex());
                // Set the parent file reference
                activationRecord.setBankDirectDebitActivationFile(bankDirectDebitActivationFile);
                if (activationRecord.getRejectionReasonEdited() == null ||
                        !activationRecord.getRejectionReasonEdited()
                                .equalsIgnoreCase("duplicatecombinationofdardr(07+08+11+12+17)itwasalreadyprocessedearlier")) {
                    records.add(activationRecord);
                }
            }
        }

        br.close();
        csvReader.close();
        return records;
    }

    public String generateNewDetails(BankDirectDebitActivationRecord activationRecord) {
        String details = activationRecord.getContract();

        if (activationRecord.getAmount() != null || activationRecord.getStartDate() != null ||
                activationRecord.getExpiryDate() != null || activationRecord.getAccount() != null ||
                activationRecord.getIban() != null) {

            details += "_" + activationRecord.getAmount() + "_" +
                    activationRecord.getStartDate() + "_" + activationRecord.getExpiryDate() + "_" +
                    activationRecord.getAccount() + "_" + activationRecord.getIban();
        }
        return details;
    }

    private void parseRecordFields(String[] cellsValues, BankDirectDebitActivationRecord activationRecord) throws ParseException {

        activationRecord.setRowIndex(Integer.parseInt(getCellValue(cellsValues[0])));
        activationRecord.setPresentmentDate(parseDate(cellsValues, 1));
        activationRecord.setBank(getStringValue(cellsValues, 3));
        activationRecord.setContract(getStringValue(cellsValues, 4));
        activationRecord.setAccount(getStringValue(cellsValues, 6));
        activationRecord.setDdaRefNo(getStringValue(cellsValues, 9));
        activationRecord.setStatus(getStringValue(cellsValues, 11));
        activationRecord.setRejectionReason(getStringValue(cellsValues, 12));
        activationRecord.setIban(getStringValue(cellsValues, 13));
        activationRecord.setAmount(cellsValues[14] != null && !cellsValues[14].isEmpty() ? Double.parseDouble(getCellValue(cellsValues[14])) : null);
        activationRecord.setStartDate(parseDate(cellsValues, 16));
        activationRecord.setExpiryDate(parseDate(cellsValues, 17));
        activationRecord.setDetails(generateNewDetails(activationRecord));
    }

    private String getStringValue(String[] cellsValues, int index) {
        if (cellsValues[index] == null || cellsValues[index].isEmpty()) return null;
        return getCellValue(cellsValues[index]);
    }

    private java.sql.Date parseDate(String[] cellsValues, int index) throws ParseException {
        if (cellsValues[index] == null || cellsValues[index].isEmpty()) return null;
        return new java.sql.Date(DateUtil.parseDateSlashed(getCellValue(cellsValues[index])).getTime());
    }

    private String getCellValue(String value) {
        if (value.startsWith("\'"))
            return value.substring(1);
        return value;
    }

    private void processRejectionReason(BankDirectDebitActivationRecord activationRecord) {

        // Normalize rejection reason in one step
        String bankReason = activationRecord.getRejectionReason().replaceAll("[ ,.]", "").toLowerCase();
        activationRecord.setRejectionReasonEdited(bankReason);
        activationRecord.setRejectCategory(DirectDebitBankRejectionReason.fromString(bankReason));
    }

    private void handleRecordException(Exception ex, BankDirectDebitActivationRecord activationRecord) {
        ex.printStackTrace();
        if (ex instanceof ParseException) {
            activationRecord.setErrorMessage("Date Parsing Exception: " + ex.getMessage());
        } else if (ex instanceof NumberFormatException) {
            activationRecord.setErrorMessage("Number Parsing Exception: " + ex.getMessage());
        } else {
            activationRecord.setErrorMessage("Exception: " + ex.getMessage());
        }
    }

    @Transactional
    public void validateRecordsAfterInsert(Integer pageNb, Integer pageSize, Long fileId) {
        try {
            List<Map<String, Object>> l = bankDirectDebitActivationRecordRepository
                    .findRecordWithMatchedDdfByBankDirectDebitActivationFileId(fileId, PageRequest.of(pageNb, pageSize));
            if (l.isEmpty()) return;

            l.forEach(m -> {
                BankDirectDebitActivationRecord record = (BankDirectDebitActivationRecord) m.get("record");
                Long ddfId = (Long) m.get("ddfId");
                logger.info("record id  " + record.getId() + "; ddfId: " + ddfId);

                record.setDirectDebitFileId(ddfId);
            });

            bankDirectDebitActivationRecordRepository.saveAll(l.stream()
                    .map(m -> (BankDirectDebitActivationRecord) m.get("record"))
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ACC-2777
    public void notifyAccountantAboutNewRejectionReasons(List<BankDirectDebitActivationRecord> records, List<String> newRejectionReasons) {

        List<String> saveNewRejectionReasons = new ArrayList<>();
        for (String rejectionReason : newRejectionReasons) {
            List<BankDirectDebitActivationRecord> matchedRecords = records.stream()
                    .filter(record -> record.getRejectionReason() != null &&
                            record.getRejectionReason().equals(rejectionReason))
                    .collect(Collectors.toList());
            if (prepareRejectionReasonsReportAndSendEmailToAccountant(rejectionReason, matchedRecords) &&
                    rejectionReason.length() <= 200) {
                saveNewRejectionReasons.add(rejectionReason);
            }
        }

        if (!saveNewRejectionReasons.isEmpty()) {
            PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
            Picklist ddRejectionReasonsList = picklistRepository.findByCode(AccountingModule.PICKLIST_KNOWN_DD_REJECTION_REASONS);
            for (String rejectionReason : saveNewRejectionReasons) {
                ddRejectionReasonsList.addItem(rejectionReason);
            }
            picklistRepository.save(ddRejectionReasonsList);
        }
    }

    private boolean prepareRejectionReasonsReportAndSendEmailToAccountant(String rejectionReason, List<BankDirectDebitActivationRecord> records) {
        try {
            if (records == null || records.isEmpty()) return false;

            NewDDRejectionReasonsReport report = new NewDDRejectionReasonsReport(records);

            Map<String, String> parameters = new HashMap();
            parameters.put("rejection_reason", rejectionReason);
            parameters.put("html_table", report.render());

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("new_rejection_reason",
                            parameters, Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_GEORGE_EMAIL_NEW_DD_REJECTION_REAOSNS_REPORT),
                            "New Rejection reason.");

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

//    @Async
    public void uploadFileByRPA(BankDirectDebitActivationFile file, User user) {

        SecurityContext sc = SecurityContextHolder.getContext();
        try {
            // ACC-9606 Check if this file is duplicated
            file.getAttachments().set(0, Storage.getAttchment(file.getAttachments().get(0).getId()));
            if (!checkNewFile(file)) return;

            UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(user, null, null);
            sc.setAuthentication(authReq);
            file.setAddedByRPA(true);
            logger.info("file id:" + file);
            bankDirectDebitActivationFileRepository.save(file);

            Map<String, Object> payload = new HashMap<>();
            payload.put("entityId", file.getId().toString());

            if (QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                    new Object[]{UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString(),
                            Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)})) {

                AccountingEntityProperty a = new AccountingEntityProperty();
                a.setKey(AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL);
                // BGT name
                a.setOrigin(file);
                a.setPurpose(UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString());
                a.setValue(objectMapper.writeValueAsString(payload));
                accountingEntityPropertyRepository.save(a);
            } else {
                BackgroundTaskHelper.createBGTParsingStatementUploaded(
                        UploadStatementEntityType.BankDirectDebitActivationByRPAFile,
                        UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString(),
                        payload);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            sc.setAuthentication(null);
        }
    }

    // ACC-9606 Check if this file is duplicated
    public boolean checkNewFile(BankDirectDebitActivationFile bankDirectDebitActivationFile) throws IOException {
        return checkNewFile(parseRecordsInMemory(bankDirectDebitActivationFile));
    }

    public boolean checkNewFile(List<BankDirectDebitActivationRecord> newRecords) {
        logger.info("newRecords size: " + newRecords.size());
        if (newRecords.isEmpty()) return false;

        List<String> allDetails = newRecords.stream()
                .map(r -> {
                    logger.info("details: " + r.getDetails());
                    return r.getDetails();
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (allDetails.isEmpty()) return false;

        Long countMatchedRecords = bankDirectDebitActivationRecordRepository.countRecordsByDetails(
                newRecords.get(0).getBankDirectDebitActivationFile() != null ?
                        newRecords.get(0).getBankDirectDebitActivationFile().getId() :
                        null,
                allDetails);

        logger.info("countMatchedRecords: " + countMatchedRecords +
                "; allDetails.size(): " + allDetails.size());

        // If countMatchedRecords equals allDetails, it means all newRecords already existed.
        if (countMatchedRecords != null && countMatchedRecords == allDetails.size()) {
            logger.info("The activation file is a duplicate of a previous one -> exiting");
            return false;
        }

        return true;
    }
}