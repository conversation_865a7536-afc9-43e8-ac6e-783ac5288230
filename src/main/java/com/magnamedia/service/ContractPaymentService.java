package com.magnamedia.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.ContractPaymentConfirmationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.*;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.PaymentReceiptHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Mahfoud
 */

@Service
public class ContractPaymentService {
    private static final Logger logger = Logger.getLogger(ContractPaymentService.class.getName());

    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private Utils utils;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;

    public List<ContractPayment> getUniqueAndSortedPayments(
            Contract contract,
            List<ContractPayment> payments,
            DateTime ddaStartDate) {

        logger.log(Level.INFO, "contractPayment size : {0}", payments.size());
        List<ContractPayment> l = new ArrayList<>();
        Set<String> s = new HashSet<>();
        payments.stream()
                .filter(cp -> ddaStartDate == null ||
                        (new DateTime(cp.getDate()).plusHours(1).isAfter(ddaStartDate) &&
                                cp.getPaymentType().getCode().equals("monthly_payment")))
                .forEach(cp -> {
                    String key = "date: " + new LocalDate(cp.getDate()).toString("yyyy-MM") +
                            "; type: " + cp.getPaymentType().getCode();
                    logger.log(Level.INFO, "contractPayment: {0}; contract amount {1}",
                            new Object[]{key, cp.getAmount()});

                    if (s.contains(key)) return;
                    s.add(key);

                    l.add(cp);
                });
        Collections.reverse(l);

        return getUnReceivedPayment(l, contract);
    }

    public List<ContractPayment> getUnReceivedPayment(List<ContractPayment> l, Contract c) {
        Set<String> existingPayments = paymentRepository.getAllReceivedPayment(c)
                .stream()
                .map(o -> o[0] + "_" + new LocalDate(o[1]).toString("yyyy-MM"))
                .collect(Collectors.toSet());

        return l.stream()
                .filter(cp -> !existingPayments.contains(
                        cp.getPaymentType().getCode() + "_" + new LocalDate(cp.getDate()).toString("yyyy-MM")))
                .collect(Collectors.toList());
    }

    public List<Object[]> getUniqueAndSortedMonthlyPayments(List<Object[]> payments) {
        logger.log(Level.INFO, "Payments size : {0}", payments.size());
        if (payments.isEmpty()) return new ArrayList<>();

        List<Object[]> l = new ArrayList<>();
        Set<String> s = new HashSet<>();
        payments.forEach(p -> {
            String key = new LocalDate(p[2]).toString("yyyy-MM");
            logger.log(Level.INFO, "key: {0}; payment amount {1}",
                    new Object[]{key, p[1]});

            if (s.contains(key)) return;
            s.add(key);
            l.add(p);
        });
        Collections.reverse(l);
        return l;
    }

    public DateTime getLastMonthlyPaymentDate(Contract contract) {
        PicklistItem monthlyPayment = PicklistHelper.getItem(
                AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        DateTime lastPayment = paymentService.getLastReceivedMonthlyPaymentDate(contract);
        List<ContractPayment> payments = contractPaymentRepository.findNonDDContractPayment(
                contract, monthlyPayment);

        if (lastPayment == null && payments.isEmpty()) return null;

        DateTime lastCp = payments.isEmpty() ? null : new DateTime(payments.get(0).getDate());
        logger.log(Level.INFO, "contract id : {0}; lastCp date : {1}",
                new Object[]{contract.getId(), (lastCp == null ? "null" : lastCp)});

        if (lastCp ==  null) return lastPayment.withTimeAtStartOfDay();

        if (lastPayment ==  null) return lastCp.withTimeAtStartOfDay();

        return lastPayment.isAfter(lastCp) ?
                lastPayment.withTimeAtStartOfDay() : lastCp.withTimeAtStartOfDay();
    }

    @Transactional
    public void savePaymentsMapList(
            ContractPaymentTerm contractPaymentTerm,
            List<LinkedHashMap> paymentsMapList,
            String authorizationCode,
            String oldContracts) throws Exception {

        savePaymentsMapList(contractPaymentTerm, paymentsMapList, authorizationCode, null, oldContracts, false);
    }

    @Transactional
    public void savePaymentsMapList(
            ContractPaymentTerm contractPaymentTerm,
            List<LinkedHashMap> paymentsMapList,
            String authorizationCode,
            String transferReference,
            String oldContracts,
            boolean ignoreSendNotification) throws Exception {

        List<ContractPayment> payments = new ArrayList();
        for (LinkedHashMap hashMap : paymentsMapList) {
            ContractPayment cp = utils.readObjectFromLinkedHasMap(hashMap, ContractPayment.class);
            payments.add(cp);
        }

        savePayments(contractPaymentTerm, payments, authorizationCode, transferReference, oldContracts, ignoreSendNotification);
    }

    public void savePayments(
            ContractPaymentTerm contractPaymentTerm,
            List<ContractPayment> payments,
            String authorizationCode) {

        savePayments(contractPaymentTerm, payments, authorizationCode, null, null, false);
    }

    public void savePayments(
            ContractPaymentTerm contractPaymentTerm,
            List<ContractPayment> payments,
            String authorizationCode,
            String transferReference,
            String oldContracts,
            boolean ignoreSendNotification) {

        List<ContractPayment> nonDdPayments = new ArrayList<>();
        for (ContractPayment payment : payments) {
            if (!payment.getConfirmed()) {
                payment.setContractPaymentTerm(contractPaymentTerm);
                if (payment.getAmount() > 0) {
                    contractPaymentRepository.save(payment);
                    if (!payment.getPaymentMethod().equals(PaymentMethod.DIRECT_DEBIT)){
                        if(!payment.isConfirmationTodoAdded()) nonDdPayments.add(payment);

                        // todo check with Shaban
                        //  sendEmailForNonDDPayment(payment);
                    }
                } else if (payment.getId() != null) {
                    contractPaymentRepository.delete(payment);
                }
            }
        }

        if (nonDdPayments.isEmpty()) return;
        // ACC-6033
        addConfirmationTodoForPayment(payments, authorizationCode, transferReference, oldContracts, ignoreSendNotification);
    }

    private void addConfirmationTodoForPayment(
            List<ContractPayment> payments,
            String authorizationCode,
            String transferReference,
            String oldContracts,
            boolean ignoreSendNotification) {

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();
        todo.setContractPaymentTerm(payments.get(0).getContractPaymentTerm());
        todo.setSource(ContractPaymentConfirmationToDo.Source.INITIAL_PAYMENTS);
        todo.setPaymentType(payments.get(0).getPaymentType());
        todo.setPaymentMethod(payments.get(0).getPaymentMethod());
        todo.setPayingOnline(payments.get(0).isOnline());
        todo.setDescription(payments.get(0).getDescription());
        todo.setAttachments(payments.get(0).getAttachments());
        todo.setAuthorizationCode(authorizationCode);
        todo.setTransferReference(transferReference);
        todo.setOldContracts(oldContracts);
        todo.setIgnoreSendNotification(ignoreSendNotification);  // ACC-7573

        payments.stream()
                .filter(p -> p.getPaymentMethod().equals(PaymentMethod.WIRE_TRANSFER))
                .findFirst()
                .ifPresent(p -> todo.setExpectedDate(p.getDate()));

        payments.forEach(p -> {
            logger.info("amount: " + p.getAmount() + " date: " + p.getDate());
            ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
            wrapper.setContractPaymentConfirmationToDo(todo);
            wrapper.setContractPayment(p);
            wrapper.setReplacedBouncedPaymentId(p.getReplaceOf()!=null?p.getReplaceOf().getId():null);
            wrapper.setPaymentDate(p.getDate());
            wrapper.setProrated(p.getIsProRated());
            wrapper.setInitial(p.getIsInitial());
            wrapper.setAmount(p.getAmount());
            wrapper.setActualReceivedAmount(p.getAmount());
            wrapper.setVatPaidByClient(p.getVat()!=null && !p.getVat().equals(0.0));
            wrapper.setIncludeWorkerSalary(p.getIncludeWorkerSalary());
            // ACC-8422 todo check if SALES send the worker salary and includeWorkerSalary
            wrapper.setWorkerSalary(p.getWorkerSalary());
            wrapper.setDescription(p.getDescription());
            wrapper.setPaymentType(p.getPaymentType());
            wrapper.setAffectedByAdditionalDiscount(p.getAdditionalDiscountAmount() != null &&
                    p.getAdditionalDiscountAmount() > 0.0);
            wrapper.setMoreAdditionalDiscount(p.getMoreAdditionalDiscount());
            wrapper.setDiscountAmount(p.getDiscountAmount());
            wrapper.setSubType(p.getSubType());

            todo.getContractPaymentList().add(wrapper);
        });

        Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoController.class)
                .createConfirmationToDo(todo);
    }

    private void sendEmailForNonDDPayment(ContractPayment payment) throws Exception {
        if (payment.getPaymentMethod().equals(PaymentMethod.DIRECT_DEBIT)) return;

        Contract contract = payment.getContractPaymentTerm().getContract();
        Client client = contract.getClient();
        String frontEndUrl = Setup.getParameter(
                Setup.getCurrentModule(),
                AccountingModule.PARAMETER_FRONT_END_URL);


        Map vatObject = new HashMap();
        vatObject.put("isProRated", payment.getIsProRated() != null ? payment.getIsProRated() : false);
        vatObject.put("vat", payment.getVat() != null ? payment.getVat() : 0.0);
        vatObject.put("vatPercent", payment.getVatPercent() != null ? payment.getVatPercent() : 0.0);
        vatObject.put("visaFees", payment.getVisaFees() != null ? payment.getVisaFees() : 0.0);
        vatObject.put("visaFeesWithoutVAT", payment.getVisaFeesWithoutVAT() != null ? payment.getVisaFeesWithoutVAT() : 0.0);
        vatObject.put("includeWorkerSalary", payment.getIncludeWorkerSalary() != null ? payment.getIncludeWorkerSalary() : false);
        vatObject.put("paymentWithoutVAT", payment.getPaymentWithoutVAT() != null ? payment.getPaymentWithoutVAT() : 0.0);

        logger.log(Level.SEVERE, "start converting to string");

        logger.log(Level.SEVERE, "Single Object: " + new ObjectMapper().writeValueAsString(vatObject));

        String url = frontEndUrl + "#!/client/payments/add/" + client.getId() + "/" + contract.getId() +
                "?typeOfPayment=" + payment.getPaymentType().getId() + "&paymentMethod=" + payment.getPaymentMethod().getValue() +
                "&date=" + DateUtil.formatDateDashed(payment.getDate()) + "&status=RECEIVED&vatObject=" + new ObjectMapper().writeValueAsString(vatObject) +
                "&amount=" + payment.getAmount() + "&returnPage=contarctPayments";

        logger.log(Level.SEVERE, "Whole Object: " + url);

        String paymentLink = "<a href='" + url + "'>Payment Confirmation</a>";

        Map<String, String> parameters = new HashMap<>();
        parameters.put("client_name", client.getName());
        parameters.put("amount", String.valueOf(payment.getAmount()));
        parameters.put("payment_method", payment.getPaymentMethod().getLabel());
        parameters.put("payment_link", paymentLink);

        // send email
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("EmailForNonDDPayment",
                        parameters, Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_NON_DD_PAYMENT_EMAIL),
                        "New non direct debit payment for confirmation");
    }

    public ContractPayment createContractPaymentForDda(ContractPaymentTerm cpt, Date paymentDate, Double amount, PicklistItem paymentType) {

        ContractPayment contractPayment = new ContractPayment();
        contractPayment.setContractPaymentTerm(cpt);
        contractPayment.setDate(paymentDate);
        contractPayment.setPaymentType(paymentType);
        contractPayment.setDescription(paymentType.getName());
        contractPayment.setPaymentMethod(PaymentMethod.CARD);
        contractPayment.setVatPaidByClient(!paymentType.hasTag(ContractPayment.NO_VAT_TAG));
        contractPayment.setIsInitial(false);

        if (amount == null && PaymentHelper.isMonthlyPayment(paymentType)) {
            Map<String, Object> m = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate(paymentDate));
            contractPayment.setAmount((Double) m.get("amount"));
            contractPayment.setIncludeWorkerSalary((boolean) m.get("includeWorkerSalary"));
            contractPayment.setWorkerSalary((Double) m.get("workerSalary"));
            contractPayment.setDiscountAmount((Double) m.get("discountAmount"));
            contractPayment.setAdditionAmount(0D);
            contractPayment.setAdditionalDiscountAmount((Double) m.get("additionalDiscountAmountPerPayment"));
            contractPayment.setMoreAdditionalDiscount((Double) m.get("moreAdditionalDiscount"));
        } else {
            contractPayment.setAmount(amount);

            // ACC-8422
            boolean includeWorkerSalary = cpt.getContract().isMaidVisa() &&
                    paymentType.getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) &&
                    !calculateDiscountsWithVatService.isDuringPremiumPeriod(cpt, paymentDate);
            contractPayment.setIncludeWorkerSalary(includeWorkerSalary);
            contractPayment.setWorkerSalary(includeWorkerSalary ? cpt.getContract().getWorkerSalaryNew() : 0D);
            contractPayment.setDiscountAmount(includeWorkerSalary ? cpt.getDiscount() : 0D);
        }

        return contractPayment;
    }

    // ACC-7321_ACC-8205
    public Map<String, Boolean> getParameterFeatureMatchingDDcWithCPT() {
        Map<String, Boolean> result = new HashMap<>();
        result.put("matchWithDDs", false);
        result.put("matchWithPayments", false);
        try {
            Map<String, Object> m = new ObjectMapper().readValue(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_ENABLE_MATCHING_THE_DDC_WITH_DD_OR_PAYMENT),
                    Map.class);

            result.put("matchWithDDs", ((String) m.get("matchWithDDs")).trim().equalsIgnoreCase("on"));
            result.put("matchWithPayments", ((String) m.get("matchWithPayments")).trim().equalsIgnoreCase("on"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    //ACC-6993
    public Map<String, Object> checkIfPaymentMatchesWithPTC(Map<String, Object> map) {

        double vatPercent = DiscountsWithVatHelper.getVatPercent();
        double difference = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), (AccountingModule.PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_TWO_AMOUNT_FOR_MATCH_PTC)));
        double differenceProRatedAndDiscount = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                (AccountingModule.PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_PRORATED_DISCOUNT_FOR_MATCH_PTC)));

        List<AbstractPaymentTypeConfig> paymentTypeConfigs = (List<AbstractPaymentTypeConfig>) map.get("paymentTypeConfigs");
        List<ContractPayment> contractPayments = (List<ContractPayment>) map.get("contractPayments");
        AbstractPaymentTerm abstractPaymentTerm = (AbstractPaymentTerm) map.get("abstractPaymentTerm");


        int creditNoteMonths = (int) map.computeIfAbsent("creditNoteMonths", k -> 0);
        double creditNoteAmountPerMonth = calculateAmountPerMonth((Double) map.get("creditNoteAmount"), creditNoteMonths);

        int additionalDiscountMonths = (int) map.computeIfAbsent("additionalDiscountMonths", k -> 0);
        double additionalDiscountPerMonth = calculateAmountPerMonth((Double) map.get("additionalDiscount"), additionalDiscountMonths);

        int waivedMonths = (int) map.computeIfAbsent("waivedMonths", k -> 0);
        boolean isOneMonthAgreement = (boolean) map.computeIfAbsent("isOneMonthAgreement", k -> false);
        boolean isProrated = (boolean) map.computeIfAbsent("isProrated", k -> false);
        boolean isProratedPlusMonth = (boolean) map.computeIfAbsent("isProratedPlusMonth", k -> false);
        DateTime contractStartDate = map.get("contractStartDate") instanceof Long ?
                new DateTime(map.get("contractStartDate")).withTimeAtStartOfDay() :
                DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(map.get("contractStartDate").toString()).withTimeAtStartOfDay();
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(Utils.parseValue(map.get("contractId"), Long.class));

        logger.info("additionalDiscountPerMonth: " + additionalDiscountPerMonth +
                "; creditNoteAmountPerMonth: " + creditNoteAmountPerMonth);

        DateTime startWaivedDate = contract.getIsProRated() ?
                contractStartDate.plusMonths(1).dayOfMonth().withMinimumValue() :
                contractStartDate;

        for (ContractPayment contractPayment : contractPayments) {
            logger.info("contractPayment Id: " + contractPayment.getId() + "; Date: " + contractPayment.getDate());

            // 1-Check If payment type excluded from ddc check
            if (contractPayment.getPaymentType().hasTag(ContractPayment.EXCLUDED_FROM_DDC_CHECK_TAG)) {
                logger.info("Payment type code: " + contractPayment.getPaymentType().getCode() + " Excluded from ddc check -> ignore");
                continue;
            }

            // 2-Check If payment is Waived
            if ((contractPayment.getAmount() < difference || contractPayment.isWaived()) &&
                    (contract.isMaidVisa() && waivedMonths > 0 &&
                        new DateTime(contractPayment.getDate()).isAfter(startWaivedDate.minusMillis(1)) &&
                        new DateTime(contractPayment.getDate()).isBefore(startWaivedDate.plusMonths(waivedMonths).dayOfMonth().withMinimumValue()))) {
                continue;
            }

            // 3-Check If the DDC PTC doesn’t have this payment type
            AbstractPaymentTypeConfig t = paymentTypeConfigs.stream()
                    .filter(p -> {
                        if (!p.getType().getCode().equals(contractPayment.getPaymentType().getCode())) return false;
                        if (PaymentHelper.isMonthlyPayment(p.getType())) return true;

                        int directDebitCount = contract.getIsProRated() ? contract.getPaymentsDuration() : contract.getPaymentsDuration() - 1;
                        int endsAfter = p.getEndsAfter() != null && p.getEndsAfter() <= directDebitCount ? p.getEndsAfter() : directDebitCount;
                        DateTime contractEndDate = contractStartDate.plusMonths(endsAfter).withDayOfMonth(1);

                        DateTime startDate = new DateTime(contractStartDate).plusMonths(p.getStartsOn()).withTimeAtStartOfDay();
                        if (!startDate.toString("yyyy-MM").equals(contractStartDate.toString("yyyy-MM"))) {
                            startDate = startDate.dayOfMonth().withMinimumValue();
                        }
                        DateTime endDate = p.getRecurrence() == null || p.getRecurrence() == 0 ?
                                startDate.dayOfMonth().withMaximumValue() :
                                contractEndDate;

                        logger.info("startDate: " + startDate + " endDate: " + endDate);

                        return new DateTime(contractPayment.getDate()).isAfter(startDate.minusMillis(1)) &&
                                new DateTime(contractPayment.getDate()).isBefore(endDate.plusMillis(1));
                    })
                    .findFirst().orElse(null);

            logger.info("t id: " + (t == null || t.getId() == null ? "NULL" : t.getId()));

            if (t == null) {
                return new HashMap<String, Object>(){{
                    put("matched", false);
                    put("contractPaymentNotMatched", contractPayment);
                }};
            }

            // 4-Check If payment is ProRated
            if (contractPayment.getIsProRated()) {
                map.put("proRatedDate", new LocalDate(contractPayment.getDate()));
                map.put("monthlyPaymentAmount", t.getAmount());
                double proRatedAmount = calculateDiscountsWithVatService.getProRatedAmount(map);

                logger.info("proRatedAmount: " + proRatedAmount);
                if (Math.abs(contractPayment.getAmount() - proRatedAmount) > differenceProRatedAndDiscount) {
                    return new HashMap<String, Object>(){{
                        put("matched", false);
                        put("contractPaymentNotMatched", contractPayment);
                    }};
                }
                continue;
            }

            // 5- Calculate PTC final Amount: Apply Discount & Credit Notes
            Double ptcFinalAmount = t.getAmount();
            if (t.getAffectedByAdditionalDiscount()) {
                if (t.getType().hasTag(ContractPayment.NO_VAT_TAG)) {
                    ptcFinalAmount = ptcFinalAmount - additionalDiscountPerMonth - creditNoteAmountPerMonth ;
                } else {
                    // Additional Discount & Addition
                    if (additionalDiscountPerMonth > 0.0) {
                        Map<String, Object> discountPeriod = PaymentReceiptHelper.getDiscountPeriod(
                                additionalDiscountMonths, waivedMonths, contractStartDate, t,
                                isOneMonthAgreement, isProrated, isProratedPlusMonth);

                        DateTime additionalDiscountStartDate = ((DateTime) discountPeriod.get("discountStartDate")).withTimeAtStartOfDay();
                        DateTime additionalDiscountEndDate = ((DateTime) discountPeriod.get("discountEndDate")).withTimeAtStartOfDay();
                        boolean paymentDateInDiscountPeriod = new DateTime(contractPayment.getDate()).plusMillis(1).isAfter(additionalDiscountStartDate) &&
                                new DateTime(contractPayment.getDate()).isBefore(additionalDiscountEndDate.plusMillis(1));

                        logger.info("additionalDiscountStartDate: " + additionalDiscountStartDate +
                                "; additionalDiscountEndDate: " + additionalDiscountEndDate +
                                "; contractPayment date: " + contractPayment.getDate());

                        // Apply Additional Discount from paymentTypeConfigs Amount:
                        // Discount & Addition: Y = ROUND(( (X / 1.05) + Addition if any - Discount if any) * 1.05)
                        if (paymentDateInDiscountPeriod) {
                            // ROUND
                            ptcFinalAmount = (double) Math.round(
                                    //   * 1.05
                                    DiscountsWithVatHelper.getAmountPlusVat(
                                            //                   (X / 1.05)                       - Discount if any
                                            DiscountsWithVatHelper.getAmountWithoutVat(ptcFinalAmount, vatPercent) - additionalDiscountPerMonth, vatPercent));
                            logger.info("After apply Additional Discount, Ptc amount: " + ptcFinalAmount);
                        }
                    }

                    // Credit note
                    if (creditNoteAmountPerMonth > 0.0) {
                        Map<String, Object> discountPeriod = PaymentReceiptHelper.getDiscountPeriod(
                                creditNoteMonths, waivedMonths, contractStartDate, t,
                                isOneMonthAgreement, isProrated, isProratedPlusMonth);

                        DateTime creditNoteStartDate = ((DateTime) discountPeriod.get("discountStartDate")).withTimeAtStartOfDay();
                        DateTime creditNoteEndDate = ((DateTime) discountPeriod.get("discountEndDate")).withTimeAtStartOfDay();
                        boolean paymentDateInDiscountPeriod = new DateTime(contractPayment.getDate()).plusMillis(1).isAfter(creditNoteStartDate) &&
                                new DateTime(contractPayment.getDate()).isBefore(creditNoteEndDate.plusMillis(1));

                        logger.info("creditNoteStartDate: " + creditNoteStartDate +
                                "; creditNoteEndDate: " + creditNoteEndDate +
                                "; contractPayment date: " + contractPayment.getDate());

                        // Apply Credit note from paymentTypeConfigs Amount:
                        //  Credit Notes: Z = ROUND(( (Y / 1.05) - Credit Note Discount) * 1.05)
                        if (paymentDateInDiscountPeriod) {
                            // ROUND
                            ptcFinalAmount = (double) Math.round(
                                    //   * 1.05
                                    DiscountsWithVatHelper.getAmountPlusVat(
                                            //                  (Y / 1.05)                        - Credit Note Discount
                                            DiscountsWithVatHelper.getAmountWithoutVat(ptcFinalAmount, vatPercent) - creditNoteAmountPerMonth, vatPercent));
                            logger.info("After apply Credit note Discount, Ptc amount: " + ptcFinalAmount);
                        }
                    }
                }
            }

            // 6-Check normal discount
            if (PaymentHelper.isMonthlyPayment(contractPayment.getPaymentType()) &&
                    t.getDiscountEffectiveAfter() != null && t.getDiscount() != null && t.getDiscount() != 0.0) {
                DateTime discountedPeriodStartDate = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(
                        contractStartDate.toDate(), isProrated, abstractPaymentTerm, t.getDiscountEffectiveAfter()));

                if (new DateTime(contractPayment.getDate()).isAfter(discountedPeriodStartDate.minusMillis(1))) {
                    ptcFinalAmount = ptcFinalAmount - t.getDiscount();
                    logger.info("Discounted amount: " + t.getDiscount());
                }
            }



            // 7-Check from amount and discount
            Double amount = contractPayment.getAmount();
            logger.info("payment amount: " + amount + "; Ptc amount: " + ptcFinalAmount);
            if (Math.abs(amount - ptcFinalAmount) > difference) {
                return new HashMap<String, Object>(){{
                    put("matched", false);
                    put("contractPaymentNotMatched", contractPayment);
                }};
            }
        }

        logger.info("true");
        return new HashMap<String, Object>(){{
            put("matched", true);
            put("contractPaymentNotMatched", null);
        }};
    }

    public double calculateAmountPerMonth(Double amount, Integer months ){

        if(amount == null || months == null) return 0.0;

        amount = amount > 0.0 ? amount : 0.0;
        months = months > 0 ? months : 0;

        double amountPerMonth = 0.0;
        if (amount > 0.0 && months > 0) amountPerMonth = amount / months;

        return amountPerMonth;
    }

    public void updateRequiredForUnfitToWorkForLinkedContractPayment(Payment payment, boolean requiredForUnfitToWork) {
        ContractPayment cp = paymentService.getContractPayment(payment);

        if (cp == null) return;
        logger.info("Updating RequiredForUnfitToWork for found ContractPayment ID: " + cp.getId());
        cp.setRequiredForUnfitToWork(requiredForUnfitToWork);
        contractPaymentRepository.save(cp);
    }

    public static List<ContractPayment> getAllDdbPayments(
            ContractPaymentTerm cpt,
            boolean withAllPayments,
            List<DirectDebitStatus> notAllowedStatuses) {

        //monthly payment
        SelectQuery<ContractPayment> query = new SelectQuery<>(ContractPayment.class);
        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
        query.filterBy("date", ">=", new LocalDate().dayOfMonth().withMinimumValue().toDate());
        query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
        query.filterBy("directDebit.category", "=", DirectDebitCategory.B);
        query.filterBy("paymentType.code", "=", "monthly_payment");
        query.sortBy("creationDate", false);

        if (!withAllPayments) {
            query.filterBy("date", "<=", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());
            query.filterBy("directDebit.status", "not in", notAllowedStatuses);
        }

        return query.execute();
    }

    public static List<ContractPayment> getAllDdaPayments(
            ContractPaymentTerm cpt,
            boolean withAllPayments,
            Date endDate,
            List<DirectDebitStatus> notAllowedStatuses) {

        SelectQuery<ContractPayment>  query = new SelectQuery<>(ContractPayment.class);
        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
        query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
        query.filterBy("directDebit.category", "=", DirectDebitCategory.A);
        query.sortBy("creationDate", false);

        if (endDate != null) {
            query.filterBy("date", "<=", endDate);
        }  else if (!withAllPayments) {
            query.filterBy("date", "<=", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());
        }

        if (!withAllPayments) {
            query.filterBy("directDebit.MStatus", "not in", notAllowedStatuses);
        }

        List<ContractPayment> l =  query.execute();

        if (withAllPayments) return l;
        logger.info("contractPayments size: " + l.size());

        // ACC-9222 Handle All Payments in contract first month, non-Monthly and start on '0' to the flow
        return Setup.getApplicationContext()
                .getBean(ClientPayingViaCreditCardService.class)
                .handleCurrentPaymentsAlreadyCreated(cpt, l);
    }
}