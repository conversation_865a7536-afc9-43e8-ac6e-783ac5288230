package com.magnamedia.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.*;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ketrawi
 *         Acc-2826
 */

@Service
public class AccountantTodoService {

    protected static final Logger logger = Logger.getLogger(AccountantTodoService.class.getName());

    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private MailService mailService;

    public void sendMoneyTransferEmail(String beneficiary, String mobile, Double amount) {
        Map<String, Object> params = new HashMap<>();
        params.put("title", "Bank Transfer to Money Transfer Agency");
        params.put("beneficiary", beneficiary);
        params.put("mobile", mobile);
        params.put("city", "Dubai");
        params.put("amount", "AED " + String.format("%,.2f", amount));

        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_EMAIL));

        if (recipients.size() > 0) {
            mailService.sendEmail(recipients,
                    new TemplateEmail("Bank Transfer to Money Transfer Agency",
                            "MoneyTransferAgencyEmail", params),
                    null);
        }
    }

    /*public void sendERPNotificationToApprover(ClientRefundToDo clientRefundToDo, User approver) {
        NotificationService notificationService = Setup.getApplicationContext().getBean(NotificationService.class);
        PaymentRequestPurposeRepository paymentRequestPurposeRepository = Setup.getRepository(PaymentRequestPurposeRepository.class);
        ClientRepository clientRepository = Setup.getRepository(ClientRepository.class);
        PaymentRequestPurpose requestPurpose = paymentRequestPurposeRepository.findOne(clientRefundToDo.getPurpose().getId());

        logger.info("enter sendERPNotificationToApprover");

        Client client = clientRepository.findOne(clientRefundToDo.getClient().getId());

        String notificationBody = "A new client refund was added by " + clientRefundToDo.getRequesterUserName() +
                " for " + client.getName() + ", and it is waiting for your approval.";

        String link = "/accounting/client-refund-approvals/details/" + clientRefundToDo.getId();

        // send notification
        Notification notification = new Notification();
        notification.setTitle("new refund request is added");
        notification.setBody(notificationBody);
        notification.setLink(link);
        notification.setDate(new Date());
        notification.setModule(Setup.getCurrentModule());
        notification.setUser(approver);

        notificationService.sendNotification(notification);
    }*/

    public SalaryCurrency getSalaryCurrencyFromItem(PicklistItem item){
        if (item == null || item.getCode() == null) return SalaryCurrency.AED;

        String itemCode = item.getCode().toLowerCase();
        switch (itemCode){
            case "aed":
                return SalaryCurrency.AED;
            case "usd":
                return SalaryCurrency.USD;
            case "eur":
                return SalaryCurrency.EUR;
            case "qr":
                return SalaryCurrency.QR;
        }

        return SalaryCurrency.AED;
    }


    public boolean validateCreateBankTransferFromClientRefund(ClientRefundToDo clientRefundToDo) {
        boolean validTaskName = clientRefundToDo.getTaskName().contains(
                ClientRefundTodoType.BANK_TRANSFER_CREATED.toString());
        boolean validNumberOfMonthlyPayments = clientRefundToDo.getNumberOfMonthlyPayments() == null ||
                clientRefundToDo.getNumberOfMonthlyPayments() == 0;
        boolean validRequiredPayments = !clientRefundToDo.isConditionalRefund() ||
                clientRefundToDo.getRequiredPayments() == null ||
                !clientRefundToDo.getRequiredPayments().stream()
                        .map(payment -> paymentRepository.findOne(payment.getId()))
                        .anyMatch(payment -> !payment.getStatus().equals(PaymentStatus.RECEIVED) &&
                                !BooleanUtils.toBoolean(payment.getReplaced()));

        logger.info("validTaskName: " + validTaskName);
        logger.info("validNumberOfMonthlyPayments: " + validNumberOfMonthlyPayments);
        logger.info("validRequiredPayments: " + validRequiredPayments);
        logger.info("checkRequiredPayments: " + clientRefundToDo.isCheckForRequiredPayments());

        return validTaskName && validNumberOfMonthlyPayments &&
                (!clientRefundToDo.isCheckForRequiredPayments() || validRequiredPayments);
    }

    public void validateAccountantAction(ObjectNode objectNode) {
        PayrollAccountantTodo t = payrollAccountantTodoRepository.findOne(
                new ObjectMapper().convertValue(objectNode.get("id"), Long.class));

        if (t.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString())) {
            Double amount = new ObjectMapper().convertValue(objectNode.get("amount"), Double.class);

            if (!amount.equals(t.getAmount())) {
                throw new RuntimeException("Amount couldn't be changed");
            }
        } else if (t.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString())) {
            Double total = new ObjectMapper().convertValue(objectNode.get("total"), Double.class);
            Double charges = new ObjectMapper().convertValue(objectNode.get("charges"), Double.class);
            if (!total.equals(charges + t.getAmount())) {
                throw new RuntimeException("Total Amount should be equal to amount + charges");
            }
        }
    }

    public boolean managerActionValidation() {
        return CurrentRequest.getUser() != null &&
                CurrentRequest.getUser().hasPosition("bank_transfer_reviewer_user_position");
    }

    public void managerAction(PayrollAccountantTodo body) {
        PayrollAccountantTodo todo = payrollAccountantTodoRepository.findOne(body.getId());
        if (!todo.getManagerAction().equals(PayrollAccountantTodoManagerAction.PENDING))
            throw new RuntimeException("Action has already been taken");

        todo.setManagerAction(body.getManagerAction());
        todo.setManagerActionBy(CurrentRequest.getUser());
        payrollAccountantTodoRepository.save(todo);
    }

    public List<EmailRecipient> bankTransferRejectionEmailRecipients(
            PayrollAccountantTodo t) {

        User requester = t.getRequesterUser();
        if(requester == null ||
                requester.getLoginName().equals("admin") ||
                requester.getLoginName().equals("guest")) return new ArrayList<>();

        User refundApprover = null;
        if(t.getClientRefundToDo() != null)
            refundApprover = t.getClientRefundToDo().getManagerActionBy();
        else if(t.getExpensePayment() != null) {
            List<ExpenseRequestTodo> l = t.getExpensePayment().getExpenseRequestTodos();
            if(!l.isEmpty()) refundApprover = l.get(0).getApproveHolder();
        }

        List<EmailRecipient> recipients = new ArrayList<>();
        if(requester != null && requester.getEmail() != null) {
            recipients.add(new Recipient(requester.getEmail(), requester.getUsername()));
        }
        if(refundApprover != null && refundApprover.getEmail() != null) {
            recipients.add(new Recipient(refundApprover.getEmail(), refundApprover.getUsername()));
        }

        return recipients;
    }

    public void sendTransferManagerRejectionEmail(PayrollAccountantTodo entity){
        PayrollAccountantTodo t  = payrollAccountantTodoRepository.findOne(entity.getId());
        List<EmailRecipient> recipients = bankTransferRejectionEmailRecipients(t);

        if(!recipients.isEmpty()) {
            String subject = "Bank Transfer Rejected For " + t.getType();

            if(t.getExpensePayment() != null) {
                List<ExpenseRequestTodo> l = t.getExpensePayment().getExpenseRequestTodos();
                if(!l.isEmpty() && l.get(0).getExpense() != null)
                    subject +=  (" - " + l.get(0).getExpense().getName());
            }

            Map<String, String> parameters = new HashMap<>();
            parameters.put("beneficiary", t.getBeneficiary());
            parameters.put("type", t.getType());
            parameters.put("description", t.getDescription());
            parameters.put("amount_aed", String.valueOf(t.getAmountAED().intValue()));
            parameters.put("amount", String.valueOf(t.getTotal().intValue()));
            parameters.put("requester", t.getRequester());
            parameters.put("approvals", t.getApprovalFlow());
            parameters.put("due_since", t.getDueSince());
            parameters.put("rejection_notes", entity.getRejectionNotes());

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("bank_transfer_manager_rejected",
                            parameters,
                            recipients.stream().map(EmailRecipient::getEmail).collect(Collectors.joining(",")),
                            subject);
        }
    }

    public void sendTransferCooRejectionEmail(PayrollAccountantTodo entity){
        PayrollAccountantTodo t  = payrollAccountantTodoRepository.findOne(entity.getId());
        List<EmailRecipient> recipients = bankTransferRejectionEmailRecipients(t);

        if(!recipients.isEmpty())
            mailService.sendEmail(recipients,
                    new TextEmail(
                            entity.getLabel() + " Rejected",
                            "COO rejection notes: " + entity.getRejectionNotes()),
                    EmailReceiverType.Prosepect);
    }

    public void onApprovalAction(
            PayrollAccountantTodo todo,
            PayrollAccountantTodoManagerAction action) {

        PayrollAccountantTodo t = payrollAccountantTodoRepository.findOne(todo.getId());

        if (t.getClientRefundToDo() != null && t.getClientRefundToDo().getId() != null) {
            Setup.getApplicationContext()
                    .getBean(ClientRefundService.class)
                    .closeClientRefundTodo(todo.getClientRefundToDo(), todo.getCeoAction().toString());
        } else if (t.getExpensePayment() != null && t.getExpensePayment().getId() != null) {
            closeExpensePayment(t, action);
        }
    }

    public void closeExpensePayment(
            PayrollAccountantTodo todo,
            PayrollAccountantTodoManagerAction action) {

        ExpensePayment p = expensePaymentRepository.findOne(
                todo.getExpensePayment().getId());
        logger.info("applying post manager/coo bank transfer action on expense payment: " + p.getId());

        switch(action) {
            case APPROVED:
                p.setStatus(ExpensePaymentStatus.PAID);
                p.setCompleted(Boolean.TRUE);
                expensePaymentRepository.save(p);
                break;
            case REJECTED:
                p.setStopped(true);
                p.updateExpensesStatus(ExpenseRequestStatus.REJECTED);
                expensePaymentRepository.save(p);
                break;
        }
    }

    //ACC-5358
    public void updateBankTransferInfo(PayrollAccountantTodo todo) {
        if (todo.getIban() != null && todo.getAccountName() != null && todo.getAccountNumber() != null) return;

        if (todo.getClientRefundToDo() == null && todo.getExpensePayment() == null) return;

        if (todo.getClientRefundToDo() != null) {
            if (!todo.getClientRefundToDo().getMethodOfPayment()
                    .equals(ClientRefundPaymentMethod.BANK_TRANSFER)) return;
            if (todo.getAccountName() == null) {
                todo.setAccountName(todo.getClientRefundToDo().getClient().getAccountName());
            }
            if (todo.getIban() == null) {
                todo.setIban(todo.getClientRefundToDo().getClient().getClientIBAN());
            }
        } else if (todo.getExpensePayment() != null) {
            if (!todo.getExpensePayment().getMethod().equals(ExpensePaymentMethod.BANK_TRANSFER)
                    || todo.getExpensePayment().getBeneficiaryType() == null) return;

            switch (todo.getExpensePayment().getBeneficiaryType()) {
                case SUPPLIER:
                    Supplier supplier = Setup.getRepository(SupplierRepository.class)
                            .findOne(todo.getExpensePayment().getBeneficiaryId());
                    if (supplier == null) return;

                    if (todo.getIban() == null) {
                        todo.setIban(supplier.getIban());
                    }
                    if (todo.getAccountName() == null) {
                        todo.setAccountName(supplier.getAccountName());
                    }
                    if (todo.getAccountNumber() == null) {
                        todo.setAccountNumber(supplier.getAccountNumber());
                    }
                    if (todo.getSwift() == null) {
                        todo.setSwift(supplier.getSwift());
                    }
                    if (todo.getAddress() == null) {
                        todo.setAddress(supplier.getAddress());
                    }
                    break;
                case OFFICE_STAFF:
                    OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class)
                            .findOne(todo.getExpensePayment().getBeneficiaryId());
                    if (staff == null) return;

                    if (todo.getIban() == null) {
                        todo.setIban(staff.getIbanDestination());
                    }
                    if (todo.getSwift() == null) {
                        todo.setSwift(staff.getSwiftDestination());
                    }
                    if (todo.getAccountName() == null) {
                        todo.setAccountName(staff.getAccountNameDestination());
                    }
                    if (todo.getAccountNumber() == null) {
                        todo.setAccountNumber(staff.getAccountNumberDestination());
                    }
                    if (todo.getAddress() == null) {
                        todo.setAddress(staff.getFullAddressDestination());
                    }
                    break;
                default:
                    return;
            }
        }

        payrollAccountantTodoRepository.silentSave(todo);
    }

    public void bulkAccountantAction(List<String> ids) {
        List<PayrollAccountantTodo> todos;

        if(ids != null && !ids.isEmpty()) {
            todos = payrollAccountantTodoRepository.findAllById(ids.stream()
                    .map(p -> Utils.parseValue(p, Long.class)).collect(Collectors.toList()));
        } else if(ids == null) {
            SelectQuery<PayrollAccountantTodo> query = new SelectQuery<>(PayrollAccountantTodo.class);
            query.filterBy("completed", "=", false);
            query.filterBy("stopped", "=", false);

            todos = query.execute();
        } else {
            return;
        }


        for (PayrollAccountantTodo todo : todos) {
            try {
                if (!todo.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString()) ||
                        !Arrays.asList("BUCKET_REPLENISHMENT", "REFUNDS", "EXPENSES_SUPPLIER")
                                .contains(todo.getBeneficiaryType().get("beneficiaryType"))) {
                    continue;
                }
                logger.info("todo id : " + todo.getId());

                ObjectNode objectNode = objectMapper.createObjectNode();
                objectNode.put("id", todo.getId());
                objectNode.put("amount", todo.getAmount());

                Setup.getApplicationContext()
                        .getBean(PayrollAccountantTodoController.class)
                        .accountantAction(objectNode);
            } catch (Exception e) {
                logger.info("Error in bulkAccountantAction : " + e.getMessage());
                e.printStackTrace();
            }
        }

        logger.info("Bulk Accountant Action has been taken on todos size : " + todos.size());
    }
}