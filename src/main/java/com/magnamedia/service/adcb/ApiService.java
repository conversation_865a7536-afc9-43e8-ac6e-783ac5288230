package com.magnamedia.service.adcb;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @creation_date 4/8/2025
 * */
@Service
public class ApiService {

    @Autowired
    private SSLContextProvider sslProvider;
    @Autowired
    private AccessTokenManager tokenManager;
    @Autowired
    private ObjectMapper objectMapper;

    /*@Value("${adcb.api.base.url}")
    private String apiBaseUrl;*/

    public GenericApiResponse callGenericApi(
            String apiBaseUrl, String endpoint, String method,
            Map<String, String> queryParams, Object body, HashMap<String, Object> accessTokenData) throws Exception {
        String accessToken = tokenManager.getAccessToken(accessTokenData);
        SSLContext sslContext = sslProvider.getSSLContext((String) accessTokenData.get("password"));

        try (CloseableHttpClient client = HttpClients.custom().setSSLContext(sslContext).build()) {
            // Build URI with query parameters
            URIBuilder uriBuilder = new URIBuilder(apiBaseUrl + endpoint);
            if (queryParams != null && !queryParams.isEmpty()) {
                for (Map.Entry<String, String> param : queryParams.entrySet()) {
                    uriBuilder.addParameter(param.getKey(), param.getValue());
                }
            }
            URI uri = uriBuilder.build();

            // Create appropriate HTTP request based on method
            HttpRequestBase request = createHttpRequest(method.toUpperCase(), uri);
            
            // Set authorization header
            request.setHeader("Authorization", "Bearer " + accessToken);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            // Add body for POST, PUT, PATCH requests
            if (body != null && request instanceof HttpEntityEnclosingRequestBase) {
                String jsonBody = (body instanceof String) ? (String) body : objectMapper.writeValueAsString(body);
                StringEntity entity = new StringEntity(jsonBody, "UTF-8");
                ((HttpEntityEnclosingRequestBase) request).setEntity(entity);
            }

            // Execute request
            try (CloseableHttpResponse response = client.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = response.getEntity() != null ? EntityUtils.toString(response.getEntity()) : "";
                
                return new GenericApiResponse(
                    statusCode,
                    response.getStatusLine().getReasonPhrase(),
                    responseBody,
                    request.getURI().toString(),
                    method.toUpperCase()
                );
            }
        }
    }

    private HttpRequestBase createHttpRequest(String method, URI uri) {
        switch (method) {
            case "GET":
                return new HttpGet(uri);
            case "POST":
                return new HttpPost(uri);
            case "PUT":
                return new HttpPut(uri);
            case "DELETE":
                return new HttpDelete(uri);
            case "PATCH":
                return new HttpPatch(uri);
            case "HEAD":
                return new HttpHead(uri);
            case "OPTIONS":
                return new HttpOptions(uri);
            default:
                throw new IllegalArgumentException("Unsupported HTTP method: " + method);
        }
    }

    // Response wrapper class
    @Setter
    @Getter
    @AllArgsConstructor
    public static class GenericApiResponse {
        private int statusCode;
        private String statusMessage;
        private String responseBody;
        private String requestUrl;
        private String requestMethod;
    }
}