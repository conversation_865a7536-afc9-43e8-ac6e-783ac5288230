package com.magnamedia.service.adcb;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Component
@Log4j2
public class AccessTokenManager {

    /*@Value("${adcb.client.id}")
    private String clientId;

    @Value("${adcb.client.secret}")
    private String clientSecret;

    @Value("${adcb.scope}")
    private String scope;

    @Value("${adcb.grant.type}")
    private String grantType;

    @Value("${adcb.token.url}")
    private String tokenUrl;*/

    private static String token;
    private static long expiryTime = 0;

    public synchronized String getAccessToken(HashMap<String, Object> body) {
        if (token == null || System.currentTimeMillis() >= expiryTime) {
            refreshAccessToken(body);
        }
        return token;
    }

    /*private void refreshAccessToken() {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("client_id", clientId);
        map.add("client_secret", clientSecret);
        map.add("scope", scope);
        map.add("grant_type", grantType);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
        log.info("response status : {}", response.getStatusCode());
        log.info("response body : {}", response.getBody());

        token = (String) response.getBody().get("access_token");
        long expiresIn = Long.parseLong(String.valueOf(response.getBody().get("expires_in")));
        log.info("expiresIn : {}", expiresIn);
        expiryTime = System.currentTimeMillis() + (expiresIn * 1000);
    }*/

    private void refreshAccessToken(HashMap<String, Object> body) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            UriComponentsBuilder builder = UriComponentsBuilder.fromUri(new URI((String) body.get("tokenUrl")))
                    .queryParam("client_id", body.get("clientId"))
                    .queryParam("client_secret", body.get("clientSecret"))
                    .queryParam("scope", body.get("scope"))
                    .queryParam("grant_type", body.get("grantType"));

            HttpEntity<Void> request = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            log.info("response status : {}", response.getStatusCode());
            log.info("response body : {}", response.getBody());

            token = (String) response.getBody().get("access_token");
            long expiresIn = Long.parseLong(String.valueOf(response.getBody().get("expires_in")));
            expiryTime = System.currentTimeMillis() + (expiresIn * 1000);

            log.info("expiresIn : {}", expiresIn);
        } catch (Exception e) {
            log.error("Error : {}", e.getMessage());
            e.printStackTrace();
        }
    }
}