package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.module.type.DDMessagingSubType;
import com.magnamedia.repository.FlowProgressPeriodRepository;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> <PERSON>
 */

@Entity
public class FlowSubEventConfig extends BaseEntity {
    
    @Enumerated(EnumType.STRING)
    private FlowSubEventName name;

    @Enumerated(EnumType.STRING)
    private RequiredDocument requiredDocument;

    @Enumerated(EnumType.STRING)
    private RequiredAction requiredAction;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FlowEventConfig flowEventConfig;
    
    private int maxTrials;
    private int maxReminders;
    private int defaultPeriod;
    private boolean terminateContractOnMaxTrials = true;
    private boolean terminateContractOnMaxReminders = true;
    private boolean infiniteReminder = false;

    public FlowSubEventName getName() {
        return name;
    }

    public void setName(FlowSubEventName name) {
        this.name = name;
    }

    public FlowEventConfig getFlowEventConfig() {
        return flowEventConfig;
    }

    public void setFlowEventConfig(FlowEventConfig flowEventConfig) {
        this.flowEventConfig = flowEventConfig;
    }

    public int getMaxTrials() {
        return maxTrials;
    }

    public void setMaxTrials(int maxTrials) {
        this.maxTrials = maxTrials;
    }

    public int getMaxReminders() {
        return maxReminders;
    }

    public void setMaxReminders(int maxReminders) {
        this.maxReminders = maxReminders;
    }

    public boolean isTerminateContractOnMaxTrials() {
        return terminateContractOnMaxTrials;
    }

    public void setTerminateContractOnMaxTrials(boolean terminateContractOnMaxTrials) {
        this.terminateContractOnMaxTrials = terminateContractOnMaxTrials;
    }

    public boolean isTerminateContractOnMaxReminders() {
        return terminateContractOnMaxReminders;
    }

    public void setTerminateContractOnMaxReminders(boolean terminateContractOnMaxReminders) {
        this.terminateContractOnMaxReminders = terminateContractOnMaxReminders;
    }
    
    @Basic(fetch = FetchType.LAZY)
    public List<FlowProgressPeriod> getProgressPeriods() {
        return Setup.getRepository(FlowProgressPeriodRepository.class).findByFlowSubEventConfig(this);
    }

    public RequiredDocument getRequiredDocument() { return requiredDocument; }

    public void setRequiredDocument(RequiredDocument requiredDocument) { this.requiredDocument = requiredDocument; }

    public RequiredAction getRequiredAction() { return requiredAction; }

    public void setRequiredAction(RequiredAction requiredAction) { this.requiredAction = requiredAction; }

    public int getDefaultPeriod() { return defaultPeriod; }

    public void setDefaultPeriod(int defaultPeriod) { this.defaultPeriod = defaultPeriod; }

    public boolean isInfiniteReminder() { return infiniteReminder; }

    public void setInfiniteReminder(boolean infiniteReminder) { this.infiniteReminder = infiniteReminder; }

    public enum FlowSubEventName {
        NO_SIGNATURE(DDMessagingSubType.NO_SIGNATURE),

        PENDING_PAYMENT(DDMessagingSubType.PENDING_PAYMENT),
        PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS(DDMessagingSubType.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS),

        INITIAL_FLOW_FOR_DDB(DDMessagingSubType.INITIAL_FLOW_FOR_DDB),
        INITIAL_FLOW_FOR_DDA(DDMessagingSubType.INITIAL_FLOW_FOR_DDA),
        MONTHLY_REMINDER(DDMessagingSubType.MONTHLY_REMINDER),
        // ACC-6840
        INSUFFICIENT_FUNDS(DDMessagingSubType.INSUFFICIENT_FUNDS),
        INSUFFICIENT_FUNDS_MV(DDMessagingSubType.INSUFFICIENT_FUNDS),

        EXCEEDING_DAILY_LIMITS(DDMessagingSubType.EXCEEDING_DAILY_LIMITS),
        EXCEEDING_DAILY_LIMITS_MV(DDMessagingSubType.EXCEEDING_DAILY_LIMITS),

        EXPIRED_CARD(DDMessagingSubType.EXPIRED_CARD),

        ACCOUNT_ISSUE(DDMessagingSubType.ACCOUNT_ISSUE),
        ACCOUNT_ISSUE_MV(DDMessagingSubType.ACCOUNT_ISSUE),

        CC_OTHER_ISSUES(DDMessagingSubType.OTHER_ISSUES),
        CC_OTHER_ISSUES_MV(DDMessagingSubType.OTHER_ISSUES),

        // Just to send a message
        TOKEN_DELETED(DDMessagingSubType.TOKEN_DELETED),
        DD_Rejection(DDMessagingSubType.DD_Rejection),
        DD_SIGNING_OFFER(DDMessagingSubType.DD_SIGNING_OFFER),


        MISSING_BANK_INFO(DDMessagingSubType.MISSING_BANK_INFO),
        GENERATE_DDS_AFTER_PAID_END_DATE(DDMessagingSubType.GENERATE_DDS_AFTER_PAID_END_DATE),
        // ACC-8954 Extension Flow
        PAYMENT_REMINDER_EXTENSION(DDMessagingSubType.PAYMENT_REMINDER_EXTENSION),

        // DEPRECATED
        NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY(DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY, true),
        PAYMENT_REMINDER(DDMessagingSubType.PAYMENT_REMINDER, true),
        SIGNING_OFFER_AND_CC_PAYMENT_ALLOWED(DDMessagingSubType.SIGNING_OFFER_AND_CC_PAYMENT_ALLOWED, true),
        SIGNING_OFFER_AND_CC_PAYMENT_IS_NOT_ALLOWED(DDMessagingSubType.SIGNING_OFFER_AND_CC_PAYMENT_IS_NOT_ALLOWED, true);

        private final boolean deprecated;


        public DDMessagingSubType getDdMessagingSubType() {
            return ddMessagingSubType;
        }

        private DDMessagingSubType ddMessagingSubType;

        FlowSubEventName(DDMessagingSubType ddMessagingSubType) {
            this.deprecated = false;
            this.ddMessagingSubType = ddMessagingSubType;
        }

        FlowSubEventName(DDMessagingSubType ddMessagingSubType, boolean deprecated) {
            this.deprecated = deprecated;
            this.ddMessagingSubType = ddMessagingSubType;
        }

        public DDMessagingSubType getMessagingSubType() {
            return ddMessagingSubType;
        }

        public boolean isDeprecated() { return deprecated; }
    }

    public enum RequiredAction implements LabelValueEnum {
        CLIENT_PROVIDES_SIGNATURE("Signature"),
        CLIENT_PROVIDES_MISSING_BANK_INFO("Bank Info"),
        ONLINE_CREDIT_CARD_PAYMENT_RECEIVED("Online Credit Card Payment Received"),
        NEW_DD_CONFIRMED("New DD Confirmed");
        private final String label;

        RequiredAction(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }

    public enum RequiredDocument implements LabelValueEnum {
        EID("EID");

        private final String label;

        RequiredDocument(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }
}