package com.magnamedia.entity.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractSummaryPaymentStatus;
import com.magnamedia.module.type.ContractSummaryPaymentType;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.FlowProcessorService;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.joda.time.LocalDate;

import java.util.Arrays;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class ClientMgmtPaymentDetailsDto {
    public enum Source { PAYMENT, CONTRACT_PAYMENT, GENERATION_PLAN, CPT, NOT_EXISTING}

    @JsonIgnore
    private Contract contract;

    @JsonIgnore
    private ContractSummaryPaymentType type;
    @JsonIgnore
    private String typeCode;
    @JsonIgnore
    private String typeLabel;
    @JsonIgnore
    private LocalDate dueDateLocal;

    private String paymentType = "NULL";
    private String paymentMethod = "NULL";
    private String dueDate = "NULL";
    private String paymentStatus = "NULL";
    private String dateReceived = "NULL";
    private Double amount = 0.0;
    private Double discount = 0.0;
    private Double vat = 0.0;
    private Boolean isWaived = false;
    private String paymentLink = "NULL";
    private Boolean paymentIncludesSalary = false;
    private Boolean isDue = false;
    private String tokenizedPaymentStatus = "NULL";
    private Boolean dueDateForNextMonth = false;
    private Boolean dueDateForLastMonth = false;
    private String dueDatePlusOneDay = "NULL";
    private String dueDatePlusOneMonth = "NULL";

    @JsonIgnore
    private boolean isReceived = false;
    private Source source;

    public ClientMgmtPaymentDetailsDto(Contract contract) {
        this.contract = contract;
    }

    public void setDueDate(Date dueDate) {
        LocalDate currentDate = new LocalDate();
        this.dueDateLocal = new LocalDate(dueDate);

        this.dueDate = dueDateLocal.toString("yyyy-MM-dd");

        if (dueDateLocal.isBefore(currentDate.dayOfMonth().withMinimumValue())) {
            this.isDue = !Arrays.asList(
                        ContractSummaryPaymentStatus.RECEIVED.getLabel(),
                        ContractSummaryPaymentStatus.WAIVED.getLabel(),
                        ContractSummaryPaymentStatus.NEW_CONTRACT_PAYMENT_DOESNT_EXIST.getLabel())
                    .contains(this.paymentStatus);
        } else if (dueDateLocal.toString("yyyy-MM").equals(currentDate.toString("yyyy-MM"))) {
            this.isDue = !this.isWaived;
        } else {
            int interval;
            if (Setup.getApplicationContext().getBean(FlowProcessorService.class)
                    .isPayingViaCreditCard(contract)) {
                if (Arrays.asList(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING,
                        ContractSummaryPaymentType.MONTHLY_RECURRING).contains(this.type) &&
                        Setup.getRepository(PaymentRepository.class)
                                .existByPaymentRecurringStatusAndDate(this.contract, dueDateLocal.toDate())) {
                    interval = 0;
                } else {
                    interval = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAM_INTERVAL_FOR_DUE_PAYMENTS));
                }
            } else {
                interval = 0;
            }
            this.isDue = dueDateLocal.isBefore(new LocalDate().plusDays(interval + 1));
        }
        this.dueDatePlusOneDay = dueDateLocal.plusDays(1).toString("yyyy-MM-dd");
        this.dueDatePlusOneMonth = dueDateLocal.plusMonths(1).toString("yyyy-MM-dd");

        this.dueDateForNextMonth = dueDateLocal.toString("yyyy-MM").equals(
                currentDate.plusMonths(1).toString("yyyy-MM"));
        this.dueDateForLastMonth = dueDateLocal.toString("yyyy-MM").equals(
                currentDate.minusMonths(1).toString("yyyy-MM"));
    }

    public String getPaymentType() {
        if(type != null) {
            if(typeLabel != null) {
                return type.getLabel().replace("@param@", typeLabel);
            }
            return type.getLabel();
        }
        return "";
    }

    public String getPaymentCode() {
        if(type != null) {
            if(typeCode != null) {
                return type.getCode().replace("@param@", typeCode);
            }
            return type.getCode();
        }
        return "";
    }

    public void setType(ContractSummaryPaymentType type) {
        this.type = type;
        this.paymentType = getPaymentType();
    }

    public void setTypeLabel(String typeLabel) {
        this.typeLabel = typeLabel;
        this.paymentType = getPaymentType();
    }

    public void setPaymentIncludesSalary(Boolean paymentIncludesSalary) {
        if (this.contract.isMaidCc()) {
            this.paymentIncludesSalary = true;
        } else {
            this.paymentIncludesSalary = paymentIncludesSalary;
        }
    }
}